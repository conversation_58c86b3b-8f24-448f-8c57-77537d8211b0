（二）
1、列表的应用--扑克牌游戏：
现有以下三个列表：
A = ["A","2","3","4","5","6","7","8","9","10","J","Q","K"]
B = ["♠","♥","♣","♦"]
C= ["XW","DW"]
实现以下功能：
1、	程序首先提问共有多少个人？接收用户输入的人数
2、	根据用户输入的人数，来进行顺序发牌，直到所有的牌都发完（提示：使用字符串拼接，用单个列表存储），依次打印出每个人所分到的牌
输出示例：
玩家1：♠2、♣5、♦k.............
玩家2：♣3、♠9、♥5............
..............................................
3、	接下来抽出第一个玩家和第二个玩家，每个玩家随机从自己的牌中抽出三张牌，请分别打印出玩家的这三张牌，分别计算这两个玩家三张牌的得分，输出最终的赢家
计分规则是取点数之和的个位数，个位数越大的人为胜者，大小王算0。
注：代码文件命名为advanced_1.py，调试结果截图放在下方。





2、字典的应用--多级类型数据管理：
一级字典为省，二级字典为市，三级字典为县（要求里面包含你自己的家乡）
一级字典包括 福建省 陕西省
二级字典大家可以各自选择这两个省的三个市，比如厦门市、泉州市、漳州市
第三级为一个列表，大家根据自己选的市，每个市随机填充三个县(可百度查阅相关县市)，这三个县用列表进行存储
比如漳州市就有下面几个县：
 
实现以下功能：
1、	程序接收用户输入的任意县或者地级市，默认用户输入的县市在你选择的范围内
2、	遍历字典，输出该县所在的市和省
3、	输出该县所在市下面的所有县（字典中包含的那三个）
4、	利用已提供的邮政编码字典，使用程序改造你家乡所在的市下面的数据结构：
将二级字典（市）的value值从原先的县，替换成该市的邮政编码。（注意，构造要在不知道具体数据的情况下，使用循环自动替换）
邮政编码字典如下：
Postal_code={"福州市":'350000',"厦门市":'361000',"莆田市":'351000',"三明市":'365000',"泉州市":'362000',"漳州市":'363000',"南平市":'353000',"龙岩市":'364000',"宁德市":'352000',"西安市":'710000',"铜川市":'727000',"宝鸡市":'721000',"咸阳市":'712000',"渭南市":'714000',"延安市":'716000',"汉中市":'723000',"榆林市":'719000',"安康市":'725000',"商洛市":'726000'}

注：代码文件命名为advanced_2.py，调试结果截图放在下方。






