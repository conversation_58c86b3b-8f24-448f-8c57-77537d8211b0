# 手写好数据源
china_data = {
    "福建省": {
        "厦门市": ["思明区", "湖里区", "集美区"],
        "泉州市": ["鲤城区", "丰泽区", "洛江区"],
        "漳州市": ["芗城区", "龙文区", "龙海区"]
    },
    "陕西省": {
        "西安市": ["新城区", "碑林区", "莲湖区"],
        "宝鸡市": ["渭滨区", "金台区", "陈仓区"],
        "咸阳市": ["秦都区", "渭城区", "杨陵区"]
    }
}

# 1、	程序接收用户输入的任意县或者地级市，默认用户输入的县市在你选择的范围内
user_input = input("请输入县或地级市名称：")

# 2、	遍历字典，输出该县所在的市和省
# 3、	输出该县所在市下面的所有县（字典中包含的那三个）
found = False
target_province = ""
target_city = ""

# 查找输入的地名
for province, cities in china_data.items():
    for city, counties in cities.items():
        # 如果输入的是市名
        if user_input == city:
            print(f"{user_input} 位于 {province}")
            target_province = province
            target_city = city
            found = True
            break
        # 如果输入的是县名
        elif user_input in counties:
            print(f"{user_input} 位于 {province} {city}")
            target_province = province
            target_city = city
            found = True
            break
    if found:
        break

if not found:
    print("未找到该地名，请检查输入是否正确！")
    exit()  
else:
    print(f"{target_city} 下面的所有区县：")
    for county in china_data[target_province][target_city]:
        print(f"  - {county}")

# 4、	利用已提供的邮政编码字典，使用程序改造你家乡所在的市下面的数据结构：
# 将二级字典（市）的value值从原先的县，替换成该市的邮政编码。（注意，构造要在不知道具体数据的情况下，使用循环自动替换）

# 邮政编码字典
Postal_code = {"福州市":'350000',"厦门市":'361000',"莆田市":'351000',"三明市":'365000',"泉州市":'362000',"漳州市":'363000',"南平市":'353000',"龙岩市":'364000',"宁德市":'352000',"西安市":'710000',"铜川市":'727000',"宝鸡市":'721000',"咸阳市":'712000',"渭南市":'714000',"延安市":'716000',"汉中市":'723000',"榆林市":'719000',"安康市":'725000',"商洛市":'726000'}

# 只改造家乡所在市的数据结构
if target_city in china_data[target_province] and target_city in Postal_code:
    # 将县列表替换为邮政编码
    china_data[target_province][target_city] = Postal_code[target_city]
    print(f"\n{target_city}邮政编码: {china_data[target_province][target_city]}")
else:
    print(f"未找到 {target_city} 的邮政编码或该市不在数据中")


