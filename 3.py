import copy

original_data = ["<PERSON>", ["<PERSON>", "<PERSON>"], 90]

# （1）基于original_data(不能又抄一遍)再复制出两个同样的列表出来，即共三份一样的列表
copied_data_1 = copy.deepcopy(original_data)
copied_data_2 = copy.deepcopy(original_data)

# （2）第一个列表修改第一个元素"Math" 为 "English"
original_data[0] = "English"

# （3）第二个列表修改第三个元素 90 为95
copied_data_1[2] = 95

# （4）第三个列表修改的第二个元素（子列表）的第一个名字"Alice"为 "Carol"
copied_data_2[1][0] = "Carol"

# （5）依次打印上述三个列表结果
print(original_data)
print(copied_data_1)
print(copied_data_2)
