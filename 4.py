# （1）定义如下字典a = {‘name’: "zhang<PERSON>”, ‘age’:25, ‘class’:["python”, "java”, "Operatingsystems”, "DataStructures”]}
a = {"name": "zhangsan", "age": 25, "class": ['python', 'java', 'Operatingsystems', 'DataStructures']}

# （2）往上述字典a中插入新的键值对项，键名为score，值为一个字典，分别是上述每一门课的分数，例如{"python”:88,......}
a["score"] = {"python": 88, "java": 90, "Operatingsystems": 92, "DataStructures": 91}

# （3）修改字典a中，java课程的分数为61
a["score"]["java"] = 61

# （4）删除字典中的age键值对
del a["age"]

# （5）循环打印输出该字典a的每一组键值对
for k, v in a.items():
    print(k, ":", v)
