import random

A = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
B = ["♠", "♥", "♣", "♦"]
C = ["XW", "DW"]
# 1、程序首先提问共有多少个人？接收用户输入的人数
n_players = int(input("共有多少个人？ "))

# 2、根据用户输入的人数，来进行顺序发牌，直到所有的牌都发完（提示：使用字符串拼接，用单个列表存储），依次打印出每个人所分到的牌
# 输出示例：
# 玩家1：♠2、♣5、♦k.............
# 玩家2：♣3、♠9、♥5............
# ..............................................
library = []
library.extend(C)
for color in B:
    for char in A:
        library.append(color + char)

random.shuffle(library)  # 洗牌
players = []
for i in range(n_players):
    players.append(library[i::n_players])
    print("玩家" + str(i) + ":", players[i])

# 3、接下来抽出第一个玩家和第二个玩家，每个玩家随机从自己的牌中抽出三张牌，请分别打印出玩家的这三张牌，分别计算这两个玩家三张牌的得分，输出最终的赢家
# 计分规则是取点数之和的个位数，个位数越大的人为胜者，大小王算0。
cards = [[], []]
cards[0] = random.sample(players[0], 3)
cards[1] = random.sample(players[1], 3)
print("玩家1：", cards[0])
print("玩家2：", cards[1])

score = [2]
for i in range(1):
    for card in cards[i]:
        rank = card[1:]
        rank_map = {
            "A": 1,
            "J": 11,
            "Q": 12,
            "K": 13,
            "XW": 0,
            "DW": 0
        }
        if rank in rank_map:
            rank = rank_map[rank]
        score[i] += int(rank)
    print(i, score[i])

